import django_filters
from .models import Property, Unit
from django.db.models import Count, Q
from rest_framework.filters import SearchFilter


class PropertyFilter(django_filters.FilterSet):
    published = django_filters.BooleanFilter(field_name='published')
    city = django_filters.CharFilter(field_name='city', lookup_expr='exact')
    status = django_filters.CharFilter(field_name='status', lookup_expr='exact')
    property_type = django_filters.CharFilter(field_name='property_type', lookup_expr='exact')
    rental_type = django_filters.CharFilter(field_name='property_rent_details__rental_type', lookup_expr='exact')
    availability_date = django_filters.CharFilter(field_name='property_slots__date', lookup_expr='exact', method='filter_availability_date')
    number_of_units = django_filters.CharFilter(field_name='property_photos__unit', lookup_expr='exact', method='filter_availability_date')
    unit_count = django_filters.RangeFilter(method='filter_unit_count', label='Unit count range')
    q = django_filters.CharFilter(method='filter_q', label='Keyword search')

    class Meta:
        model = Property
        fields = ['property_type', 'published', 'city', 'rental_type', 'unit_count', 'q']

    def filter_availability_date(self, queryset, name, value):
        """Because unavailable dates stored in table, so while filtering for an available date exclude those
        properties which are having that date as unavailable in table."""
        if value:
            return queryset.exclude(property_slots__date=value)
        return queryset

    def filter_unit_count(self, queryset, name, value):
        """
        Annotate each Property with unit_count = Count(unit_property)
        then apply the range bounds if provided.
        `value` will be a tuple (min, max) or a Range object with .start/.stop
        """
        qs = queryset.annotate(unit_count=Count('unit_property'))
        try:
            lower, upper = value.start, value.stop
        except AttributeError:
            lower, upper = value[0], value[1]

        if lower is not None:
            qs = qs.filter(unit_count__gte=lower)
        if upper is not None:
            qs = qs.filter(unit_count__lte=upper)

        return qs

    def filter_q(self, queryset, name, value):
        return queryset.filter(
            Q(name__icontains=value) |
            Q(street_address__icontains=value) |
            Q(city__icontains=value) |
            Q(state__icontains=value)
        )


class AllListingsFilter(PropertyFilter):
    min_price = django_filters.NumberFilter(field_name='property_rent_details__rent', lookup_expr='gte')
    max_price = django_filters.NumberFilter(field_name='property_rent_details__rent', lookup_expr='lte')
    amenities = django_filters.CharFilter(method='filter_amenities', label='Amenities')
    pets_allowed = django_filters.BooleanFilter(field_name='listing_info__pets_allowed')

    def filter_amenities(self, queryset, name, value):
        amenities = value.split(',')
        return queryset.filter(property_amenities__sub_amenity__id__in=amenities)


class UnitFilter(django_filters.FilterSet):
    published = django_filters.BooleanFilter(field_name='published')
    q = django_filters.CharFilter(method='filter_q', label='Keyword search')

    class Meta:
        model = Unit
        fields = ['published', 'q']

    def filter_q(self, queryset, name, value):
        return queryset.filter(
            Q(number__icontains=value) |
            Q(type__icontains=value) |
            Q(floor_number__icontains=value) |
            Q(status__icontains=value) |
            Q(unit_rent_details__assigned_tenant__icontains=value)
        )


class DocumentFilter(django_filters.FilterSet):
    q = django_filters.CharFilter(method='filter_q', label='Keyword search')

    class Meta:
        model = Unit
        fields = ['q']

    def filter_q(self, queryset, name, value):
        return queryset.filter(
            Q(title__icontains=value) |
            Q(document_type__icontains=value) |
            Q(visibility__icontains=value) |
            Q(created_at__icontains=value)
        )

class CustomSearchFilter(SearchFilter):
    search_param = 'q'  # Use 'q' instead of 'search'